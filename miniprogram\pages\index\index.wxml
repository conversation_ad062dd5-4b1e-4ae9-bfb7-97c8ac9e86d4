<!--index.wxml-->

<!--
  引入骨架屏模板
  使用微信开发者工具自动生成的骨架屏
-->
<import src="index.skeleton.wxml"/>
<!--
  这是微信小程序的页面结构文件，相当于传统Web开发中的HTML文件

  WXML (WeiXin Markup Language) 语法说明：
  WXML是微信小程序的标记语言，基于XML语法，类似HTML但有小程序特有的扩展

  主要区别对比：

  1. 标签名称差异：
     HTML                    WXML                    作用
     <div>                  <view>                  容器元素
     <span>                 <text>                  文本元素
     <img>                  <image>                 图片元素
     <input>                <input>                 输入框（相同）
     <button>               <button>                按钮（相同）

  2. 事件绑定差异：
     HTML: onclick="handleClick()"
     WXML: bind:tap="handleClick" 或 bindtap="handleClick"

     常用事件对比：
     HTML                    WXML
     onclick                bind:tap
     onchange               bind:change
     oninput                bind:input
     onsubmit               bind:submit

  3. 数据绑定差异：
     HTML: 需要JavaScript手动操作DOM
     WXML: 使用Mustache语法 {{变量名}} 自动绑定

     示例：
     HTML: <div id="title"></div> + document.getElementById('title').innerText = data;
     WXML: <view>{{title}}</view> (数据自动同步)

  4. 条件渲染：
     HTML: 需要JavaScript控制display或删除DOM
     WXML: wx:if="{{condition}}" wx:elif="{{condition2}}" wx:else

     类似于Vue的v-if或React的条件渲染

  5. 列表渲染：
     HTML: 需要JavaScript循环创建DOM
     WXML: wx:for="{{array}}" wx:key="{{index}}"

     类似于Vue的v-for或React的map渲染

  6. 组件系统：
     HTML: 原生标签 + 第三方库组件
     WXML: 小程序原生组件 + 自定义组件 + 第三方组件库(如TDesign)

     与您熟悉的技术对比：
     - 类似于WPF的XAML标记语言
     - 类似于Android的XML布局文件
     - 类似于Vue的template模板语法
     - 类似于React的JSX语法（但更接近HTML）
-->

<!--
  根容器元素
  view: WXML的基础容器组件，相当于HTML的div元素

  属性说明：
  - class: CSS类名，用法与HTML完全相同
  - style: 内联样式，用法与HTML完全相同
  - id: 元素ID，用法与HTML完全相同

  与其他技术对比：
  - HTML: <div class="container">
  - Vue: <div class="container">
  - React: <div className="container">
  - WXML: <view class="container">
  - WPF: <Grid Style="{StaticResource ContainerStyle}">
  - Android: <LinearLayout android:layout_width="match_parent">
-->



<!--
  骨架屏显示
  当页面正在加载数据时显示骨架屏，提升用户体验
-->
<template is="skeleton" wx:if="{{loading}}" />

<!--
  主要内容区域
  显示应用的所有功能和信息
  当数据加载完成后显示真实内容
-->
<view wx:else class="container">
  <!--
    视差滚动头部区域
    实现背景图片的视差滚动效果，增强页面视觉体验
  -->
  <view class="parallax-header">
    <!--
      背景图片元素
      image: WXML的图片组件，相当于HTML的<img>标签

      属性详解：
      1. src: 图片源地址
         - 支持本地路径：/images/bg.jpg
         - 支持网络地址：https://example.com/image.jpg
         - 支持云存储地址：cloud://环境ID.文件ID（这里使用的是云存储）

      2. class: CSS类名，用于应用样式

      3. mode: 图片裁剪、缩放模式（小程序特有属性）
         常用模式对比：
         - contain: 保持比例，完整显示，可能有空白（类似CSS的object-fit: contain）
         - cover: 保持比例，填满容器，可能裁剪（类似CSS的object-fit: cover）
         - aspectFit: 保持比例，完整显示（默认值）
         - aspectFill: 保持比例，填满容器
         - widthFix: 宽度不变，高度自适应
         - heightFix: 高度不变，宽度自适应

         这里使用contain确保图片完整显示，不被裁剪

      4. style: 内联样式，使用数据绑定
         {{parallaxStyle}}: Mustache语法，绑定JS中的parallaxStyle变量
         这个变量在滚动时会动态改变，实现缩放效果

      云存储地址解析：
      cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/head.jpg
      - cloud://: 云存储协议前缀
      - cloud1-1gm190n779af8083: 云环境ID
      - 636c-cloud1-1gm190n779af8083-1365450081: 云存储实例ID
      - /head.jpg: 文件路径
    -->
    <image
      src="cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/head.jpg"
      class="parallax-bg"
      mode="contain"
      style="{{parallaxStyle}}"
    />

    <!--
      半透明遮罩层
      作用：在背景图片上添加半透明遮罩，提升文字可读性

      实现原理：
      1. 通过CSS的linear-gradient创建渐变背景
      2. 使用绝对定位覆盖在背景图片上方
      3. z-index控制层级关系

      这是UI设计中的常见技巧，确保文字在任何背景下都清晰可读
    -->
    <view class="parallax-overlay"></view>

    <!--
      头像和标题内容区
      包含Logo图片和应用标题，位于视差区域的前景
    -->
    <view class="parallax-content">
      <!--
        Logo图片组件
        t-image: TDesign组件库的图片组件，功能比原生image更丰富

        TDesign是腾讯开源的企业级设计语言和组件库
        类似于Ant Design、Element UI等前端组件库

        属性说明：
        1. src: 图片地址（云存储地址）
        2. mode: 图片显示模式
           - aspectFill: 保持比例填满容器，可能裁剪
        3. class: CSS类名
        4. shape: 图片形状（TDesign特有属性）
           - round: 圆形
           - square: 方形（默认）
        5. width/height: 图片尺寸（像素值）

        与原生image组件对比：
        - 原生image: 功能基础，性能更好
        - t-image: 功能丰富，样式更美观，但体积稍大
      -->
      <t-image
        src="cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/logo/logo.jpg"
        mode="aspectFill"
        class="logo"
        shape="round"
        width="60"
        height="60"
      />

      <!--
        应用标题文本
        text: WXML的文本组件，相当于HTML的<span>标签

        特点：
        1. 只有text组件中的文本才能被长按选中
        2. text组件内只能嵌套text组件
        3. 除了text组件，其他组件内的文本都不可选中

        与HTML对比：
        HTML: <span class="title">伽House</span>
        WXML: <text class="title">伽House</text>

        与其他技术对比：
        - Vue: <span class="title">伽House</span>
        - React: <span className="title">伽House</span>
        - WPF: <TextBlock Style="{StaticResource TitleStyle}">伽House</TextBlock>
        - Android: <TextView android:text="伽House" />
      -->
      <text class="title">伽House</text>
    </view>
  </view>

  <!--
    联系信息区域
    显示门店的联系电话、地址和公告信息
    使用条件渲染，只有当数据存在时才显示
  -->
  <view class="contact-info-container" wx:if="{{contactPhone || contactAddress || contactAnnouncement}}" hover-class="interactive-press" hover-stay-time="100">
    <!--
      wx:if 条件渲染指令（小程序特有语法）

      语法：wx:if="{{条件表达式}}"
      作用：当条件为true时渲染元素，为false时不渲染（DOM中不存在）

      条件表达式解析：
      {{contactPhone || contactAddress || contactAnnouncement}}
      - ||: 逻辑或运算符，任一条件为真则整体为真
      - 只有当至少有一个联系信息存在时，才显示整个联系信息容器

      与其他技术对比：
      - Vue: v-if="contactPhone || contactAddress || contactAnnouncement"
      - React: {(contactPhone || contactAddress || contactAnnouncement) && <div>...</div>}
      - Angular: *ngIf="contactPhone || contactAddress || contactAnnouncement"
      - WPF: Visibility="{Binding HasContactInfo, Converter={StaticResource BoolToVisibilityConverter}}"

      wx:if vs wx:elif vs wx:else：
      - wx:if: 条件为真时渲染
      - wx:elif: 前面条件为假且当前条件为真时渲染
      - wx:else: 所有前面条件都为假时渲染

      wx:if vs hidden属性：
      - wx:if: 条件为假时元素不存在于DOM中（性能更好，但切换开销大）
      - hidden: 元素始终存在于DOM中，只是隐藏显示（切换开销小，但占用内存）
    -->

    <!--
      联系信息行容器
      包含电话和地址信息，使用Flexbox布局横向排列
    -->
    <view class="contact-row">
      <!--
        联系电话信息项
        使用条件渲染，只有当电话号码存在时才显示
      -->
      <view class="contact-item contact-item-phone" wx:if="{{contactPhone}}">
        <!--
          电话图标
          t-icon: TDesign的图标组件

          属性说明：
          - name: 图标名称，"call"表示电话图标
          - class: CSS类名，用于样式控制

          TDesign图标库包含数百个常用图标：
          - call: 电话
          - location: 位置
          - notification: 通知
          - chevron-right: 右箭头
          - close: 关闭
          等等...

          类似于其他图标库：
          - Font Awesome: <i class="fa fa-phone"></i>
          - Material Icons: <i class="material-icons">phone</i>
          - Ant Design: <Icon type="phone" />
        -->
        <t-icon name="call" class="contact-icon" />

        <!-- 联系信息内容容器 -->
        <view class="contact-content">
          <!--
            标签文本
            text-unify: 统一文本样式的CSS类名
            用于保持整个应用的文本样式一致性
          -->
          <text class="contact-label text-unify">联系电话</text>

          <!--
            电话号码文本
            {{contactPhone}}: 数据绑定，显示JS中contactPhone变量的值

            数据绑定原理：
            1. JS中通过setData()更新contactPhone的值
            2. WXML自动检测数据变化并更新显示
            3. 类似于Vue的响应式数据或React的state

            数据流向：
            云数据库 → JS加载数据 → setData()更新 → WXML自动渲染
          -->
          <text class="contact-value text-unify">{{contactPhone}}</text>
        </view>
      </view>

      <!--
        地址信息项
        包含点击事件，点击后显示完整地址弹窗
      -->
      <view class="contact-item contact-item-address" wx:if="{{contactAddress}}" bind:tap="showAddressPopup">
        <!--
          bind:tap 事件绑定（小程序特有语法）

          语法：bind:事件名="处理函数名"
          作用：绑定事件处理函数，类似于HTML的onclick

          常用事件类型：
          - bind:tap: 点击事件（类似onclick）
          - bind:input: 输入事件（类似oninput）
          - bind:change: 改变事件（类似onchange）
          - bind:submit: 提交事件（类似onsubmit）

          事件绑定方式对比：
          - bind:tap: 冒泡事件，会向父元素传递
          - catch:tap: 非冒泡事件，不会向父元素传递

          与其他技术对比：
          - HTML: onclick="showAddressPopup()"
          - Vue: @click="showAddressPopup"
          - React: onClick={showAddressPopup}
          - Angular: (click)="showAddressPopup()"
        -->

        <!-- 位置图标 -->
        <t-icon name="location" class="contact-icon" />

        <view class="contact-content">
          <text class="contact-label text-unify">地址</text>

          <!--
            地址文本（限制显示行数）
            contact-value-limited: CSS类名，通过CSS限制文本显示行数
            当地址过长时，会显示省略号，点击可查看完整内容
          -->
          <text class="contact-value text-unify contact-value-limited">{{contactAddress}}</text>

          <!--
            "更多"指示器
            显示右箭头，提示用户可以点击查看更多信息
          -->
          <view class="address-more">
            <!--
              右箭头图标
              size="12": 设置图标大小为12像素
              chevron-right: 右箭头图标名称
            -->
            <t-icon name="chevron-right" size="12" />
          </view>
        </view>
      </view>
    </view>

    <!--
      公告信息容器
      单独一行显示，包含点击事件
    -->
    <view class="announcement-container" wx:if="{{contactAnnouncement}}" bind:tap="showAnnouncementPopup">
      <!-- 通知图标 -->
      <t-icon name="notification" class="announcement-icon" />

      <view class="announcement-content">
        <text class="announcement-label text-unify">公告</text>

        <!--
          公告文本（限制显示行数）
          announcement-text-limited: 限制显示2行文本，超出显示省略号
          这是常见的UI设计模式，避免长文本破坏页面布局
        -->
        <text class="announcement-text announcement-text-limited text-unify">{{contactAnnouncement}}</text>

        <!-- "更多"指示器 -->
        <view class="announcement-more">
          <t-icon name="chevron-right" size="12" />
        </view>
      </view>
    </view>
  </view>

  <!--
    静态图片展示区域
    动态渲染从云数据库获取的首页展示图片
    支持点击查看大图和轮播功能
  -->
  <view class="static-images-container">
    <!--
      动态图片展示：当管理员选择了图片时显示
    -->
    <block wx:if="{{bannerImages.length > 0}}" wx:for="{{bannerImages}}" wx:key="index">
      <view class="static-image-wrapper" hover-class="interactive-press" hover-stay-time="100">
        <t-image
          src="{{item}}"
          mode="widthFix"
          class="static-image"
          shape="round"
          bind:tap="onStaticImageTap"
          data-index="{{index}}"
        />
      </view>
    </block>


  </view>

  <!--
    功能按钮区域
    包含主要功能的快捷入口按钮
    使用固定定位，始终显示在页面底部
  -->
  <view class="function-buttons fixed-bottom">
    <!--
      约活动按钮
      t-button: TDesign的按钮组件

      属性说明：
      1. class="function-btn": CSS类名，用于自定义样式

      2. theme="primary": 按钮主题样式
         TDesign按钮主题类型：
         - primary: 主要按钮，通常为蓝色，用于主要操作
         - secondary: 次要按钮，通常为灰色边框
         - danger: 危险按钮，通常为红色，用于删除等危险操作
         - warning: 警告按钮，通常为橙色
         - success: 成功按钮，通常为绿色

      3. size="large": 按钮尺寸
         - small: 小尺寸
         - medium: 中等尺寸（默认）
         - large: 大尺寸

      4. bind:tap="goToSchedule": 点击事件绑定
         点击时调用goToSchedule方法，跳转到课程表页面

      按钮组件对比：
      - HTML: <button onclick="goToSchedule()">约活动</button>
      - Vue: <el-button @click="goToSchedule" type="primary" size="large">约活动</el-button>
      - React: <Button onClick={goToSchedule} type="primary" size="large">约活动</Button>
      - WXML: <t-button bind:tap="goToSchedule" theme="primary" size="large">约活动</t-button>
    -->
    <t-button
      class="function-btn book-activity"
      theme="primary"
      size="large"
      bind:tap="goToSchedule"
    >
      <t-icon name="calendar" slot="icon"></t-icon>
      约活动
    </t-button>

    <!--
      考勤卡按钮
      功能暂未开发完成，点击时显示"开发中"提示
    -->
    <t-button
      class="function-btn attendance-card"
      theme="primary"
      size="large"
      bind:tap="goToMembershipCard"
    >
      <t-icon name="user" slot="icon"></t-icon>
      考勤卡
    </t-button>
  </view>

  <!-- 门店公告弹出层 -->
  <t-popup 
    visible="{{showAnnouncement}}" 
    placement="center" 
    bind:close="closeAnnouncementPopup"
    class="announcement-popup"
  >
    <view class="popup-content">
      <view class="popup-header">
        <t-icon name="notification" size="20" class="popup-title-icon" />
        <text class="popup-title text-unify">门店公告</text>
      </view>
      <view class="popup-body">
        <text class="popup-text indent-text text-unify">{{contactAnnouncement}}</text>
      </view>
    </view>
    <t-icon 
      t-class="close-btn" 
      name="close-circle" 
      size="64rpx" 
      color="#fff" 
      bind:tap="closeAnnouncementPopup" 
    />
  </t-popup>
  <!-- 地址弹出层 -->
  <t-popup 
    visible="{{showAddress}}" 
    placement="center" 
    bind:close="closeAddressPopup"
    class="address-popup"
  >
    <view class="popup-content">
      <view class="popup-header">
        <t-icon name="location" size="20" class="popup-title-icon" />
        <text class="popup-title text-unify">地址</text>
      </view>
      <view class="popup-body">
        <text class="popup-text indent-text text-unify">{{contactAddress}}</text>
      </view>
    </view>
    <t-icon 
      t-class="close-btn" 
      name="close-circle" 
      size="64rpx" 
      color="#fff" 
      bind:tap="closeAddressPopup" 
    />
  </t-popup>
  <!-- 相册popup轮播图 -->
  <t-popup visible="{{showAlbumPopup}}" placement="center" bind:close="closeAlbumPopup" class="album-popup">
    <view class="album-popup-content">
      <!-- 优化后的关闭按钮，使用TDesign的t-icon图标，提升美观度和一致性 -->
      <!--
        <view class="album-popup-close" bind:tap="closeAlbumPopup">×</view>
        改为：
        <view class="album-popup-close" bind:tap="closeAlbumPopup">
          <t-icon name="close" size="32" color="#fff" />
        </view>
        说明：
        - <t-icon> 是TDesign的图标组件，类似于HTML的 <i> 或 <svg>。
        - name="close" 指定关闭图标。
        - size="32" 设置图标大小为32px。
        - color="#fff" 设置图标颜色为白色。
        - 这样更符合现代UI风格，也与其他弹窗按钮风格统一。
      -->
      <view class="album-popup-close" bind:tap="closeAlbumPopup">
        <t-icon name="close" size="32" color="#fff" />
      </view>
      <swiper current="{{albumPopupIndex}}" indicator-dots="true" circular="true" autoplay="false" style="width: 100vw; height: 90vh;">
        <block wx:for="{{albumImages}}" wx:key="index">
          <swiper-item>
            <image src="{{item}}" mode="aspectFit" style="width: 100vw; height: 90vh; background: transparent;" bind:tap="previewAlbumImage" data-index="{{index}}" />
          </swiper-item>
        </block>
      </swiper>
    </view>
  </t-popup>



  <t-toast id="t-toast" />
</view>