<!--system-settings.wxml-->
<!--
  系统设置页面结构文件
  这是小程序的系统设置页面，负责管理系统全局配置的界面结构

  页面功能：
  1. 预约取消时间设置：控制用户取消预约的时间限制
  2. 维护模式设置：系统维护状态的开关控制
  3. 联系信息管理：门店联系方式和公告信息的编辑
  4. 权限控制：确保只有管理员可以访问和修改

  设计模式：
  - 卡片式布局：每个设置项独立的卡片展示
  - 表单驱动：以表单输入为主的交互方式
  - 即时反馈：设置修改后立即生效并提示
  - 权限保护：严格的管理员权限验证
-->

<!-- 根容器 -->
<view class="container">
  <!--
    预约取消时间设置卡片

    功能说明：
    这是系统最重要的业务规则设置之一，控制用户取消预约的时间限制
    例如：设置为180分钟，表示用户在活动开始前3小时内不能取消预约

    业务意义：
    1. 保护讲师权益：避免临时取消影响课程安排
    2. 提升资源利用率：减少空置名额的浪费
    3. 维护预约秩序：培养用户良好的预约习惯

    设计考虑：
    - 时间显示：同时显示用户友好的格式（如"3小时"）和精确值（180分钟）
    - 输入验证：确保输入的时间值合理有效
    - 实时更新：修改后立即计算并显示格式化时间
  -->
  <view class="setting-card">
    <!--
      卡片头部
      包含图标和标题，提供清晰的功能标识
    -->
    <view class="card-header">
      <!--
        时间图标
        t-icon: TDesign的图标组件
        - name="time": 时间相关的图标
        - size="24": 图标大小24px

        视觉作用：
        图标帮助用户快速识别功能类型，提升界面的可读性
      -->
      <t-icon name="time" size="24" />

      <!-- 卡片标题 -->
      <text class="card-title">预约取消时间设置</text>
    </view>

    <!--
      卡片内容区域
      包含当前设置显示和修改界面
    -->
    <view class="card-content">
      <!--
        当前设置显示项
        以只读方式显示当前的时间限制设置
      -->
      <view class="setting-item">
        <!-- 设置项标签 -->
        <view class="setting-label">活动开始前可取消预约时间</view>

        <!--
          设置值显示区域
          显示格式化后的时间值，如"3 小时"或"180 分钟"
        -->
        <view class="setting-value">
          <!--
            时间数值
            {{formattedTime}}: 格式化后的时间数字
            例如：180分钟显示为"3"，时间单位显示为"小时"
          -->
          <text class="time-value">{{formattedTime}}</text>

          <!--
            时间单位
            {{timeUnit}}: 时间单位文字
            根据时间长度自动选择"小时"或"分钟"
          -->
          <text class="time-unit">{{timeUnit}}</text>
        </view>
      </view>

      <!--
        输入设置区域
        提供修改时间限制的输入界面
      -->
      <view class="input-section">
        <!--
          输入行布局
          水平排列：标签 + 输入框 + 后缀文字
        -->
        <view class="input-row">
          <!-- 输入框前缀标签 -->
          <text class="input-label">活动开始前</text>

          <!--
            时间输入框
            t-input: TDesign的输入框组件

            属性说明：
            - type="number": 数字输入类型，移动端会显示数字键盘
            - placeholder: 输入提示文字
            - value="{{cancelTimeLimit}}": 双向绑定当前值
            - bind:change: 输入内容改变时触发
            - bind:blur: 输入框失去焦点时触发
            - class: 自定义样式类

            事件处理：
            - onCancelTimeChange: 实时处理输入变化
            - onCancelTimeBlur: 输入完成后的验证和格式化
          -->
          <t-input
            type="number"
            placeholder="请输入分钟数"
            value="{{cancelTimeLimit}}"
            bind:change="onCancelTimeChange"
            bind:blur="onCancelTimeBlur"
            class="time-input"
          />

          <!-- 输入框后缀标签 -->
          <text class="input-suffix">分钟可以取消预约</text>
        </view>
      </view>

      <!--
        设置说明文字
        为用户提供功能说明和使用指导
      -->
      <view class="setting-description">
        设置学员可在活动开始前多少分钟取消预约
      </view>
    </view>
  </view>

  <!--
    联系信息设置卡片

    功能说明：
    管理门店的对外联系信息，这些信息会在首页等位置展示给用户
    包括联系电话、门店地址、门店公告等重要信息

    业务价值：
    1. 用户服务：提供用户联系门店的渠道
    2. 信息透明：公开门店的基本信息
    3. 营销推广：通过公告发布重要通知

    数据管理：
    所有联系信息存储在云数据库的系统设置表中
    修改后会在全系统生效，影响首页等多个页面的显示
  -->
  <view class="setting-card">
    <!-- 卡片头部 -->
    <view class="card-header">
      <!--
        电话图标
        使用电话图标表示联系信息相关功能
      -->
      <t-icon name="call" size="24" />
      <text class="card-title">联系信息设置</text>
    </view>

    <view class="card-content">
      <!--
        联系电话设置项
        用户可以通过首页拨打这个电话联系门店
      -->
      <view class="setting-item">
        <view class="setting-label">联系电话</view>

        <!-- 电话输入区域 -->
        <view class="setting-input">
          <!--
            电话输入框
            t-input: TDesign的输入框组件

            属性说明：
            - placeholder: 输入提示文字
            - value="{{contactPhone}}": 双向绑定电话号码
            - bind:change: 输入内容改变时触发
            - class: 自定义样式类

            数据验证：
            在onContactPhoneChange方法中会进行电话号码格式验证
          -->
          <t-input
            placeholder="请输入联系电话"
            value="{{contactPhone}}"
            bind:change="onContactPhoneChange"
            class="contact-input"
          />
        </view>
      </view>

      <!--
        门店地址设置项
        用户可以通过首页查看门店的详细地址
      -->
      <view class="setting-item">
        <view class="setting-label">门店地址</view>

        <!-- 地址输入区域 -->
        <view class="setting-input">
          <!--
            地址文本域
            t-textarea: TDesign的多行文本输入组件

            属性说明：
            - placeholder: 输入提示文字
            - value="{{contactAddress}}": 双向绑定地址信息
            - bind:change: 输入内容改变时触发
            - maxlength="200": 最大字符数限制
            - autosize: 自动调整高度

            设计考虑：
            使用文本域而不是单行输入框，因为地址信息通常较长
            自动调整高度提升用户体验
          -->
          <t-textarea
            placeholder="请输入门店地址"
            value="{{contactAddress}}"
            bind:change="onContactAddressChange"
            class="contact-textarea"
            maxlength="200"
            autosize
          />
        </view>
      </view>

      <!--
        门店公告设置项
        用于发布重要通知、活动信息、注意事项等
      -->
      <view class="setting-item">
        <view class="setting-label">门店公告</view>

        <!-- 公告输入区域 -->
        <view class="setting-input">
          <!--
            公告文本域

            属性说明：
            - maxlength="500": 公告内容限制500字符
            - 其他属性与地址输入框类似

            字符限制考虑：
            公告内容比地址更长，设置为500字符
            既能容纳足够的信息，又不会过于冗长
          -->
          <t-textarea
            placeholder="请输入门店公告"
            value="{{contactAnnouncement}}"
            bind:change="onContactAnnouncementChange"
            class="contact-textarea"
            maxlength="500"
            autosize
          />
        </view>
      </view>

      <!-- 联系信息说明 -->
      <view class="setting-description">
        <text class="normal-text">
          联系信息将显示在首页，供用户查看
        </text>
      </view>
    </view>
  </view>

  <!--
    操作按钮区域

    设计说明：
    将保存按钮独立成一个区域，突出操作的重要性
    使用主题色按钮，提供明确的操作指引
  -->
  <view class="action-section">
    <!--
      保存设置按钮
      t-button: TDesign的按钮组件

      属性说明：
      - theme="primary": 主题色按钮，突出重要操作
      - size="large": 大尺寸按钮，易于点击
      - block: 块级按钮，占满容器宽度
      - bind:tap: 点击事件处理
      - loading="{{isSaving}}": 加载状态，防止重复提交
      - class: 自定义样式类

      交互设计：
      1. 点击后显示加载状态，防止用户重复点击
      2. 保存成功后显示成功提示
      3. 保存失败后显示错误信息和重试选项
    -->
    <t-button
      theme="primary"
      size="large"
      block
      bind:tap="saveSettings"
      loading="{{isSaving}}"
      class="save-btn"
    >
      保存设置
    </t-button>
  </view>

  <!--
    提示组件区域

    组件说明：
    这些组件通常放在页面底部，用于显示各种用户反馈信息
    通过ID选择器在JavaScript中调用显示

    组件类型：
    - t-toast: 轻量级消息提示，用于成功/失败等简单反馈
    - t-message: 消息通知，用于重要信息的展示
    - t-dialog: 对话框，用于确认操作或显示详细信息

    使用方式：
    在JavaScript中通过组件ID调用相应的显示方法
    例如：this.selectComponent('#t-toast').showToast()
  -->
  <t-toast id="t-toast" />
  <t-message id="t-message" />
  <t-dialog id="t-dialog" />
</view>

<!--
  文件总结：system-settings.wxml

  这个文件实现了一个完整的系统设置页面界面，主要特点：

  1. 功能完整性：
     - 业务规则设置：预约取消时间限制
     - 系统状态控制：维护模式开关
     - 信息管理：联系方式和公告编辑
     - 操作反馈：完整的提示和确认机制

  2. 界面设计：
     - 卡片式布局：功能模块清晰分离
     - 统一的视觉语言：图标、颜色、间距保持一致
     - 响应式组件：使用TDesign组件库确保体验一致
     - 用户友好：清晰的标签、提示和说明文字

  3. 交互设计：
     - 即时反馈：输入变化立即响应
     - 状态管理：加载状态、错误状态的完整处理
     - 防误操作：重复提交保护、确认机制
     - 数据验证：输入格式和长度的限制

  4. 技术特点：
     - 数据绑定：使用双向绑定简化数据同步
     - 事件处理：完整的用户交互事件覆盖
     - 组件化：充分利用TDesign组件库
     - 可维护性：清晰的结构和命名规范

  5. 业务价值：
     - 系统管理：提供完整的系统配置能力
     - 用户服务：管理对外展示的联系信息
     - 运营支持：支持维护模式和公告发布
     - 权限控制：确保只有管理员可以修改设置

  与您熟悉的技术对比：
  - 界面结构：类似于WPF的XAML或WinForms的设计器
  - 数据绑定：类似于Angular的双向绑定或Vue的v-model
  - 组件系统：类似于React的组件化开发
  - 事件处理：类似于传统桌面应用的事件驱动模式
-->