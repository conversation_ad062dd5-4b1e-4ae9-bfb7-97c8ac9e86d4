# 伽House小程序

<div align="center">
  <img src="https://img.shields.io/badge/微信小程序-2.20.1+-green.svg" alt="微信小程序版本">
  <img src="https://img.shields.io/badge/云开发-支持-blue.svg" alt="云开发支持">
  <img src="https://img.shields.io/badge/TDesign-UI组件库-orange.svg" alt="TDesign组件库">
  <img src="https://img.shields.io/badge/状态-生产环境-success.svg" alt="项目状态">
</div>

## 📖 项目简介

伽House是一个功能完整的活动室预约管理系统，基于微信小程序云开发构建。系统支持多角色权限管理，提供完整的活动预约、考勤卡管理、讲师排课、相册管理等功能，适用于瑜伽工作室、活动室等场所的数字化管理。

**重要说明**：原本按照健身房开发，后来客户说做的不是健身房，而是活动室！教练应该是讲师、会员卡应该是考勤卡。前端的名字都改了，但是代码没改。

### ✨ 核心特色
- 🎯 **多角色权限系统** - 学员、讲师、管理员三级权限管理
- 💳 **考勤卡系统** - 支持次卡、期卡等多种考勤卡类型
- 📅 **智能排课系统** - 讲师可视化排课，学员便捷预约
- � **专业相册管理** - 瀑布流+时间轴双模式，文件夹系统，拖拽排序
- 🔔 **智能通知系统** - 预约提醒、活动通知等
- 🔄 **实时同步** - 基于云开发的实时数据同步
- 📱 **响应式设计** - 适配各种屏幕尺寸的移动设备

### 📊 项目规模
- **页面数量**: 17个页面
- **云函数**: 5个
- **数据库集合**: 10个
- **核心功能模块**: 8个
- **用户角色**: 3种（学员、讲师、管理员）
- **代码行数**: 约15,000行

## 🛠 技术架构

### 前端技术栈
- **微信小程序** - 原生小程序开发框架
- **TDesign** - 腾讯官方小程序UI组件库
- **JavaScript ES6+** - 现代JavaScript语法
- **WXSS** - 小程序样式语言
- **Grid-View** - 微信官方瀑布流组件

### 后端技术栈
- **微信云开发** - Serverless后端服务
- **云函数** - Node.js运行时
- **云数据库** - NoSQL文档数据库
- **云存储** - 文件存储服务
- **定时触发器** - 自动化任务调度

### 开发环境
- **环境ID**: `cloud1-1gm190n779af8083`
- **小程序AppID**: `wxf4efc4e381cce5e7`
- **基础库版本**: 2.20.1+

## 👥 用户角色与权限

### 🎓 学员 (Student)
- ✅ 浏览活动信息和讲师介绍
- ✅ 预约和取消活动
- ✅ 查看个人预约历史
- ✅ 管理个人考勤卡
- ✅ 查看活动详情和预约状态
- ✅ 接收预约通知和提醒

### 🧘‍♀️ 讲师 (Instructor)
- ✅ 查看个人活动安排
- ✅ 管理活动学员名单
- ✅ 查看学员预约信息
- ✅ 更新个人资料和介绍
- ✅ 接收预约相关通知

### 👨‍💼 管理员 (Admin)
- ✅ 用户管理（学员、讲师权限分配）
- ✅ 活动管理（创建、编辑、上下线）
- ✅ 考勤卡管理（发放、充值、统计）
- ✅ 相册管理（图片上传、分类、展示）
- ✅ 通知管理（发送通知、查看统计）
- ✅ 系统设置和配置管理

## 📱 功能模块

### 🏠 首页模块 (`pages/index/`)
- 品牌展示和活动室介绍
- 实景照片轮播展示
- 快速预约入口
- 联系信息和公告

### 📅 预约模块 (`pages/schedule/`)
- 可视化活动表展示
- 多维度筛选（时间、讲师、活动类型）
- 一键预约功能
- 历史活动查看

### 📋 活动详情 (`pages/course-detail/`)
- 详细活动信息展示
- 实时预约状态更新
- 学员名单查看

### 📊 活动管理 (`pages/course-management/`)
- 活动CRUD操作
- 批量管理功能
- 活动模板系统

### 📖 我的预约 (`pages/my-bookings/`)
- 预约记录管理
- 多状态筛选（已预约、已完成、已取消）
- 预约历史查询

### 👤 个人中心 (`pages/profile/`)
- 个人信息管理
- 考勤卡状态查看
- 分角色功能入口

### 📸 相册管理 (`pages/album-management/`) ⭐
- **双显示模式**：瀑布流模式、时间轴模式
- **文件夹系统**：系统文件夹 + 自定义文件夹
- **图片管理**：上传、删除、批量操作、收藏、归类
- **首页展示管理**：拖拽排序调整显示顺序
- **回收站机制**：软删除，支持恢复和永久删除
- **技术亮点**：三星相册风格UI，性能优化

### 🔔 通知管理 (`pages/notifications/`)
- 通知列表查看
- 已读/未读状态管理
- 通知详情弹窗

### 👥 用户管理 (`pages/user-management/`)
- 用户列表和搜索
- 角色权限分配
- 账号状态管理

### ⚙️ 系统设置 (`pages/system-settings/`)
- 预约取消时间设置
- 维护模式开关
- 联系信息配置

## 🗄 数据库设计

### 核心数据集合

#### 👤 用户表 (`users`)
```javascript
{
  _id: "用户唯一标识",
  openid: "微信用户标识",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  roles: ["学员", "讲师", "管理员"], // 用户角色数组
  phone: "联系电话",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

#### 🏃‍♀️ 活动表 (`courses`)
```javascript
{
  _id: "活动唯一标识",
  name: "活动名称",
  coach: ["讲师openid数组"], // 业务逻辑应通过此字段查询users表获取讲师信息
  coachName: ["讲师名字数组"], // 仅供开发维护使用，业务逻辑严禁直接使用
  startTime: "开始时间",
  endTime: "结束时间",
  duration: "活动时长(分钟)",
  venue: "活动地点",
  capacity: "活动容量",
  status: "online|offline", // 活动状态
  activityDetail: "活动详情",
  images: ["活动图片fileID数组"], // 活动图片
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

#### 📅 预约表 (`bookings`)
```javascript
{
  _id: "预约唯一标识",
  userId: "用户openid",
  courseId: "活动ID",
  cardNumber: "使用的考勤卡号",
  status: "upcoming|completed|cancelled", // 预约状态
  createTime: "预约时间",
  updateTime: "更新时间"
}
```

#### 💳 考勤卡表 (`membershipCard`)
```javascript
{
  _id: "考勤卡唯一标识",
  cardNumber: "卡号",
  userId: "绑定用户openid",
  totalTimes: "总次数",
  remainingTimes: "剩余次数",
  validFrom: "有效期开始",
  validTo: "有效期结束",
  status: "正常|已用完|已过期"
}
```

#### 📸 相册图片表 (`album_images`)
```javascript
{
  _id: "图片唯一标识",
  fileID: "云存储文件ID",
  tempFileURL: "临时访问链接",
  createTime: "创建时间",
  updateTime: "更新时间",
  isDeleted: "是否删除",
  isFavorite: "是否收藏",
  bannerOrder: "首页展示顺序",
  folderIds: ["所属文件夹ID数组"]
}
```

#### 📁 相册文件夹表 (`album_folders`)
```javascript
{
  _id: "文件夹唯一标识",
  name: "文件夹名称",
  type: "system|custom", // 系统文件夹或自定义文件夹
  systemType: "favorite|banner|trash", // 系统文件夹类型
  createTime: "创建时间",
  updateTime: "更新时间",
  imageCount: "图片数量"
}
```

#### 🔔 通知表 (`notifications`)
```javascript
{
  _id: "通知唯一标识",
  userId: "用户openid",
  type: "通知类型",
  title: "通知标题",
  content: "通知内容",
  isRead: "是否已读",
  createTime: "创建时间",
  relatedId: "关联ID"
}
```

## ☁️ 云函数架构

### 📋 管理类云函数 (`adminManagement`)
- `getCourseListPaged` - 分页获取活动列表
- `addCourse` / `updateCourse` - 活动增删改
- `updateCourseStatus` - 活动上下线
- `getUserList` - 用户管理
- `getMembershipCards` - 考勤卡管理
- `initDatabase` - 数据库初始化

### 📖 预约类云函数 (`bookingManagement`)
- `bookCourse` - 预约活动
- `cancelBooking` - 取消预约
- `getUserBookings` - 获取用户预约
- `getScheduleData` - 获取活动表数据

### 👤 用户类云函数 (`userManagement`)
- `updateUserProfile` - 更新用户资料
- `getUserInfo` - 获取用户信息
- `updateUserRoles` - 权限管理

### 🔔 通知类云函数 (`notificationManagement`)
- `sendNotification` - 发送通知
- `getNotifications` - 获取通知列表
- `markAsRead` - 标记已读
- `batchNotify` - 批量通知

### ⏰ 定时任务云函数 (`notificationScheduler`)
- `dailyReminder` - 每日提醒
- `courseReminder` - 活动提醒
- `autoCleanup` - 自动清理

## 🚀 快速开始

### 环境要求
- **微信开发者工具** 1.05.0+
- **Node.js** 14.0+
- **微信小程序基础库** 2.20.1+

### 1️⃣ 项目初始化
```bash
# 克隆项目
git clone [项目地址]
cd miniprogram-6

# 安装小程序依赖
cd miniprogram
npm install

# 安装TDesign组件库
npm install tdesign-miniprogram --save --production

# 绕过限值安装TDesign依赖
cmd /c npm i tdesign-miniprogram -S --production --%
```

### 2️⃣ 云函数部署
```bash
# 部署所有云函数
cd cloudfunctions

# 部署管理类云函数
cd adminManagement && npm install
# 在微信开发者工具中右键部署

# 部署预约类云函数
cd ../bookingManagement && npm install
# 在微信开发者工具中右键部署

# 部署用户类云函数
cd ../userManagement && npm install
# 在微信开发者工具中右键部署
```

### 3️⃣ 数据库初始化
在微信开发者工具的云开发控制台中创建以下集合：
- ✅ `users` - 用户信息
- ✅ `courses` - 活动信息
- ✅ `bookings` - 预约记录
- ✅ `membershipCard` - 考勤卡信息
- ✅ `membershipCardTemplate` - 考勤卡模板
- ✅ `coursesTemplate` - 活动模板
- ✅ `systemSettings` - 系统配置
- ✅ `album_images` - 相册图片
- ✅ `album_folders` - 相册文件夹
- ✅ `notifications` - 通知消息

### 4️⃣ 权限配置
在云开发控制台设置数据库权限：
```javascript
// 建议的安全规则示例
{
  "read": true,
  "write": "auth.openid == resource.userId" // 用户只能操作自己的数据
}
```

## 🔧 开发指南

### 本地开发
1. 使用微信开发者工具打开项目根目录
2. 确保云开发环境ID配置正确
3. 编译并在模拟器中预览
4. 使用真机调试测试完整功能

### 代码规范
- **命名规范**: 使用驼峰命名法
- **注释规范**: 关键业务逻辑必须添加注释
- **错误处理**: 所有异步操作必须包含错误处理
- **数据验证**: 用户输入数据必须进行验证

### 调试技巧
- 📊 **控制台调试**: 使用`console.log`输出关键变量
- 🔍 **网络调试**: 在开发者工具中查看云函数调用
- 💾 **数据库调试**: 在云开发控制台查看数据变化
- 📱 **真机调试**: 使用真机调试测试微信相关功能

## 🔒 安全考虑

### 数据安全
- ✅ 用户数据加密存储
- ✅ 敏感信息脱敏处理
- ✅ 数据库访问权限控制
- ✅ 云函数参数验证

### 业务安全
- ✅ 预约时间冲突检测
- ✅ 考勤卡余额验证
- ✅ 重复预约防护
- ✅ 恶意操作限制
- ✅ 图片上传安全检查

## 📈 性能优化

### 前端优化
- 🚀 **懒加载**: 图片和数据按需加载
- 💾 **缓存策略**: 合理使用本地缓存
- 🔄 **数据同步**: 增量更新减少网络请求
- 📱 **体验优化**: 加载状态和错误提示

### 后端优化
- 📊 **数据库索引**: 关键字段建立索引
- 🔍 **查询优化**: 减少不必要的数据查询
- ⚡ **云函数优化**: 减少冷启动时间
- 📈 **并发处理**: 合理处理高并发场景

## 🐛 常见问题

### Q: 云函数调用失败？
**A**: 检查云函数是否正确部署，环境ID是否匹配，参数格式是否正确。

### Q: 数据库权限错误？
**A**: 确认数据库安全规则配置，检查用户是否有相应操作权限。

### Q: 页面显示异常？
**A**: 检查数据格式是否正确，网络请求是否成功，错误处理是否完善。

### Q: 预约功能异常？
**A**: 确认考勤卡余额，检查活动状态，验证时间冲突逻辑。

### Q: 相册管理功能问题？
**A**: 检查云存储权限，确认图片格式，验证文件夹系统逻辑。

### Q: 瀑布流显示异常？
**A**: 确认grid-view组件版本，检查图片链接有效性，验证CSS样式。

## 🌟 特色功能展示

### 📸 相册管理系统 ⭐ 核心亮点
- **双显示模式**：瀑布流模式（3列瀑布流）+ 时间轴模式（按日期分组）
- **文件夹系统**：系统文件夹（收藏夹、首页展示、回收站）+ 自定义文件夹
- **拖拽排序**：首页展示图片支持拖拽调整顺序
- **平滑动画**：cubic-bezier缓动效果，渐进式延迟动画
- **性能优化**：硬件加速、懒加载、智能缓存
- **三星相册风格**：现代化UI设计，用户体验优秀

### 🎯 多角色权限系统
- **动态权限**：界面根据用户角色动态显示功能
- **安全验证**：前后端双重权限验证
- **角色切换**：支持用户多角色身份

### � 智能通知系统
- **实时通知**：预约成功、取消、提醒等
- **定时任务**：每日提醒、活动提醒
- **批量通知**：支持批量发送通知

## �📞 技术支持

如遇到技术问题，请按以下方式获取帮助：

1. 📖 查看项目文档和代码注释
2. 🔍 搜索相关技术文档
3. 💬 联系开发团队获取支持
4. 📋 查看 `prompt.md` 详细需求文档

---

<div align="center">
  <p>💝 感谢使用伽House小程序系统</p>
  <p>🌟 如果这个项目对你有帮助，请给我们一个Star！</p>
</div>

