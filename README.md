# 伽House小程序

<div align="center">
  <img src="https://img.shields.io/badge/微信小程序-2.20.1+-green.svg" alt="微信小程序版本">
  <img src="https://img.shields.io/badge/云开发-支持-blue.svg" alt="云开发支持">
  <img src="https://img.shields.io/badge/TDesign-UI组件库-orange.svg" alt="TDesign组件库">
  <img src="https://img.shields.io/badge/状态-生产环境-success.svg" alt="项目状态">
</div>

## 📖 项目简介

伽House是一个活动室预约小程序，就是把传统的"预约本"数字化了。学员可以在线预约活动，讲师可以发布活动，管理员可以管理一切。

**重要说明**：原本按照健身房开发，后来客户说做的不是健身房，而是活动室！教练应该是讲师、会员卡应该是考勤卡。前端的名字都改了，但是代码没改。

### 🎯 核心功能
- 📅 **活动预约** - 学员在线预约活动，用考勤卡扣次数
- 👨‍🏫 **活动发布** - 讲师发布活动，管理员审核上线
- 💳 **考勤卡管理** - 发放考勤卡，管理次数和有效期
- 👥 **用户管理** - 学员、讲师、管理员三种角色

### 🛠 辅助功能
- 📸 **相册管理** - 上传活动室照片给用户看
- 🔔 **通知提醒** - 预约成功、活动提醒等
- ⚙️ **系统设置** - 基本配置管理

### 📊 项目规模
- **页面数量**: 17个页面
- **云函数**: 5个
- **数据库集合**: 10个
- **用户角色**: 3种（学员、讲师、管理员）

## 🛠 技术架构

### 前端技术栈
- **微信小程序** - 原生小程序开发框架
- **TDesign** - 腾讯官方小程序UI组件库
- **JavaScript ES6+** - 现代JavaScript语法

### 后端技术栈
- **微信云开发** - Serverless后端服务
- **云函数** - Node.js运行时
- **云数据库** - NoSQL文档数据库
- **云存储** - 文件存储服务

### 开发环境
- **环境ID**: `cloud1-1gm190n779af8083`
- **小程序AppID**: `wxf4efc4e381cce5e7`
- **基础库版本**: 2.20.1+

## 👥 用户角色

### 🎓 学员
- ✅ 预约和取消活动
- ✅ 查看个人预约历史
- ✅ 管理个人考勤卡

### 🧘‍♀️ 讲师
- ✅ 查看个人活动安排
- ✅ 管理活动学员名单

### 👨‍💼 管理员
- ✅ 活动管理（创建、编辑、上下线）
- ✅ 考勤卡管理（发放、充值、统计）
- ✅ 用户管理（学员、讲师权限分配）
- ✅ 相册管理（图片上传、分类、展示）
- ✅ 系统设置和配置管理

## 📱 主要页面

### 核心页面
- **首页** (`pages/index/`) - 品牌展示，快速预约入口
- **预约页面** (`pages/schedule/`) - 活动列表，在线预约
- **我的页面** (`pages/profile/`) - 个人中心，分角色功能入口

### 管理页面
- **活动管理** (`pages/course-management/`) - 活动CRUD操作
- **活动编辑** (`pages/course-edit/`) - 新建/编辑活动
- **考勤卡管理** (`pages/membership-card-management/`) - 考勤卡发放管理
- **用户管理** (`pages/user-management/`) - 用户角色管理
- **相册管理** (`pages/album-management/`) - 图片上传管理
- **系统设置** (`pages/system-settings/`) - 基本配置

### 功能页面
- **活动详情** (`pages/course-detail/`) - 活动信息展示
- **我的预约** (`pages/my-bookings/`) - 预约记录管理
- **考勤卡** (`pages/membership-card/`) - 个人考勤卡查看
- **讲师活动表** (`pages/coach-schedule/`) - 讲师专用页面
- **通知** (`pages/notifications/`) - 通知消息查看

## 🗄 数据库设计

### 核心数据表

#### 👤 用户表 (`users`)
```javascript
{
  _id: "用户唯一标识",
  openid: "微信用户标识",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  roles: ["学员", "讲师", "管理员"], // 用户角色数组
  phone: "联系电话",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

#### 🏃‍♀️ 活动表 (`courses`)
```javascript
{
  _id: "活动唯一标识",
  name: "活动名称",
  coach: ["讲师openid数组"], // 业务逻辑通过此字段查询users表
  coachName: ["讲师名字数组"], // 仅供开发维护使用，业务逻辑禁用
  startTime: "开始时间",
  endTime: "结束时间",
  duration: "活动时长(分钟)",
  venue: "活动地点",
  capacity: "活动容量",
  status: "online|offline", // 活动状态
  activityDetail: "活动详情",
  images: ["活动图片fileID数组"],
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

#### 📅 预约表 (`bookings`)
```javascript
{
  _id: "预约唯一标识",
  userId: "用户openid",
  courseId: "活动ID",
  cardNumber: "使用的考勤卡号",
  status: "upcoming|completed|cancelled", // 预约状态
  createTime: "预约时间",
  updateTime: "更新时间"
}
```

#### 💳 考勤卡表 (`membershipCard`)
```javascript
{
  _id: "考勤卡唯一标识",
  cardNumber: "卡号",
  userId: "绑定用户openid",
  totalTimes: "总次数",
  remainingTimes: "剩余次数",
  validFrom: "有效期开始",
  validTo: "有效期结束",
  status: "正常|已用完|已过期"
}
```

### 辅助数据表

#### 📋 活动模板表 (`coursesTemplate`)
```javascript
{
  _id: "模板唯一标识",
  name: "模板名称",
  type: "活动类型",
  coach: ["讲师openid数组"],
  venue: "活动地点",
  capacity: "活动容量",
  difficulty: "难度等级",
  description: "活动描述",
  suitableFor: "适合人群"
}
```

#### 💳 考勤卡模板表 (`membershipCardTemplate`)
```javascript
{
  _id: "模板唯一标识",
  name: "模板名称",
  totalTimes: "总次数",
  validityDays: "有效天数",
  description: "模板描述",
  createTime: "创建时间"
}
```

#### ⚙️ 系统设置表 (`systemSettings`)
```javascript
{
  cancelBookingHours: "预约取消时间限制(小时)",
  maintenanceMode: "维护模式开关",
  contactPhone: "联系电话",
  storeAddress: "门店地址",
  announcement: "公告信息"
}
```

#### 📸 相册图片表 (`album_images`)
```javascript
{
  _id: "图片唯一标识",
  fileID: "云存储文件ID",
  tempFileURL: "临时访问链接",
  createTime: "创建时间",
  updateTime: "更新时间",
  isDeleted: "是否删除",
  isFavorite: "是否收藏",
  bannerOrder: "首页展示顺序",
  folderIds: ["所属文件夹ID数组"]
}
```

#### 📁 相册文件夹表 (`album_folders`)
```javascript
{
  _id: "文件夹唯一标识",
  name: "文件夹名称",
  type: "system|custom", // 系统文件夹或自定义文件夹
  systemType: "favorite|banner|trash", // 系统文件夹类型
  createTime: "创建时间",
  updateTime: "更新时间",
  imageCount: "图片数量"
}
```

#### 🔔 通知表 (`notifications`)
```javascript
{
  _id: "通知唯一标识",
  userId: "用户openid",
  type: "通知类型",
  title: "通知标题",
  content: "通知内容",
  isRead: "是否已读",
  createTime: "创建时间",
  relatedId: "关联ID"
}
```

## ☁️ 云函数

- **adminManagement** - 管理员功能
- **bookingManagement** - 预约管理
- **userManagement** - 用户管理
- **notificationManagement** - 通知管理
- **notificationScheduler** - 定时任务

## 🚀 快速开始

### 1. 项目初始化
```bash
# 克隆项目
git clone [项目地址]
cd miniprogram-6

# 安装依赖
cd miniprogram
npm install
npm install tdesign-miniprogram --save
```

### 2. 云函数部署
在微信开发者工具中右键部署所有云函数

### 3. 数据库初始化
在云开发控制台创建所需的数据库集合

### 4. 配置环境
确保云开发环境ID配置正确

## 🔧 开发指南

### 本地开发
1. 使用微信开发者工具打开项目
2. 确保云开发环境配置正确
3. 编译并在模拟器中预览

### 代码规范
- 使用驼峰命名法
- 关键业务逻辑必须添加注释
- 所有异步操作必须包含错误处理

## 🐛 常见问题

### Q: 云函数调用失败？
**A**: 检查云函数是否正确部署，环境ID是否匹配。

### Q: 预约功能异常？
**A**: 确认考勤卡余额，检查活动状态。

### Q: 相册管理问题？
**A**: 检查云存储权限，确认图片格式。

## 📞 技术支持

如遇到技术问题：
1. 📖 查看项目文档和代码注释
2. 📋 查看 `prompt.md` 详细需求文档
3. 💬 联系开发团队获取支持

---

<div align="center">
  <p>💝 感谢使用伽House预约小程序</p>
  <p>🌟 如果这个项目对你有帮助，请给我们一个Star！</p>
</div>
