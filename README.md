# 伽House小程序

<div align="center">
  <img src="https://img.shields.io/badge/微信小程序-2.20.1+-green.svg" alt="微信小程序版本">
  <img src="https://img.shields.io/badge/云开发-支持-blue.svg" alt="云开发支持">
  <img src="https://img.shields.io/badge/TDesign-UI组件库-orange.svg" alt="TDesign组件库">
  <img src="https://img.shields.io/badge/状态-生产环境-success.svg" alt="项目状态">
</div>

## 📖 项目简介

伽House是一个活动室预约小程序，就是把传统的"预约本"数字化了。学员可以在线预约活动，讲师可以发布活动，管理员可以管理一切。

**重要说明**：原本按照健身房开发，后来客户说做的不是健身房，而是活动室！教练应该是讲师、会员卡应该是考勤卡。前端的名字都改了，但是代码没改。

### 🎯 核心功能
- 📅 **活动预约** - 学员在线预约活动，用考勤卡扣次数
- 👨‍🏫 **活动发布** - 讲师发布活动，管理员审核上线
- 💳 **考勤卡管理** - 发放考勤卡，管理次数和有效期
- 👥 **用户管理** - 学员、讲师、管理员三种角色

### 🛠 辅助功能
- 📸 **相册管理** - 上传活动室照片给用户看
- 🔔 **通知提醒** - 预约成功、活动提醒等
- ⚙️ **系统设置** - 基本配置管理

### 📊 项目规模
- **页面数量**: 17个页面
- **云函数**: 5个
- **数据库集合**: 10个
- **用户角色**: 3种（学员、讲师、管理员）

## 🛠 技术架构

### 前端技术栈
- **微信小程序** - 原生小程序开发框架
- **TDesign** - 腾讯官方小程序UI组件库
- **JavaScript ES6+** - 现代JavaScript语法

### 后端技术栈
- **微信云开发** - Serverless后端服务
- **云函数** - Node.js运行时
- **云数据库** - NoSQL文档数据库
- **云存储** - 文件存储服务

### 开发环境
- **环境ID**: `cloud1-1gm190n779af8083`
- **小程序AppID**: `wxf4efc4e381cce5e7`
- **基础库版本**: 2.20.1+

## 👥 用户角色

### 🎓 学员
- ✅ 预约和取消活动
- ✅ 查看个人预约历史
- ✅ 管理个人考勤卡

### 🧘‍♀️ 讲师
- ✅ 查看个人活动安排
- ✅ 管理活动学员名单

### 👨‍💼 管理员
- ✅ 活动管理（创建、编辑、上下线）
- ✅ 考勤卡管理（发放、充值、统计）
- ✅ 用户管理（学员、讲师权限分配）
- ✅ 相册管理（图片上传、分类、展示）
- ✅ 系统设置和配置管理

## 📱 主要页面

### 核心页面
- **首页** (`pages/index/`) - 品牌展示，快速预约入口
- **预约页面** (`pages/schedule/`) - 活动列表，在线预约
- **我的页面** (`pages/profile/`) - 个人中心，分角色功能入口

### 管理页面
- **活动管理** (`pages/course-management/`) - 活动CRUD操作
- **活动编辑** (`pages/course-edit/`) - 新建/编辑活动
- **考勤卡管理** (`pages/membership-card-management/`) - 考勤卡发放管理
- **用户管理** (`pages/user-management/`) - 用户角色管理
- **相册管理** (`pages/album-management/`) - 图片上传管理
- **系统设置** (`pages/system-settings/`) - 基本配置

### 功能页面
- **活动详情** (`pages/course-detail/`) - 活动信息展示
- **我的预约** (`pages/my-bookings/`) - 预约记录管理
- **考勤卡** (`pages/membership-card/`) - 个人考勤卡查看
- **讲师活动表** (`pages/coach-schedule/`) - 讲师专用页面
- **通知** (`pages/notifications/`) - 通知消息查看

## 🗄 数据库设计

### 核心数据表
- **users** - 用户信息
- **courses** - 活动信息
- **bookings** - 预约记录
- **membershipCard** - 考勤卡信息

### 辅助数据表
- **coursesTemplate** - 活动模板
- **membershipCardTemplate** - 考勤卡模板
- **systemSettings** - 系统配置
- **album_images** - 相册图片
- **album_folders** - 相册文件夹
- **notifications** - 通知消息

## ☁️ 云函数

- **adminManagement** - 管理员功能
- **bookingManagement** - 预约管理
- **userManagement** - 用户管理
- **notificationManagement** - 通知管理
- **notificationScheduler** - 定时任务

## 🚀 快速开始

### 1. 项目初始化
```bash
# 克隆项目
git clone [项目地址]
cd miniprogram-6

# 安装依赖
cd miniprogram
npm install
npm install tdesign-miniprogram --save
```

### 2. 云函数部署
在微信开发者工具中右键部署所有云函数

### 3. 数据库初始化
在云开发控制台创建所需的数据库集合

### 4. 配置环境
确保云开发环境ID配置正确

## 🔧 开发指南

### 本地开发
1. 使用微信开发者工具打开项目
2. 确保云开发环境配置正确
3. 编译并在模拟器中预览

### 代码规范
- 使用驼峰命名法
- 关键业务逻辑必须添加注释
- 所有异步操作必须包含错误处理

## 🐛 常见问题

### Q: 云函数调用失败？
**A**: 检查云函数是否正确部署，环境ID是否匹配。

### Q: 预约功能异常？
**A**: 确认考勤卡余额，检查活动状态。

### Q: 相册管理问题？
**A**: 检查云存储权限，确认图片格式。

## 📞 技术支持

如遇到技术问题：
1. 📖 查看项目文档和代码注释
2. 📋 查看 `prompt.md` 详细需求文档
3. 💬 联系开发团队获取支持

---

<div align="center">
  <p>💝 感谢使用伽House预约小程序</p>
  <p>🌟 如果这个项目对你有帮助，请给我们一个Star！</p>
</div>
