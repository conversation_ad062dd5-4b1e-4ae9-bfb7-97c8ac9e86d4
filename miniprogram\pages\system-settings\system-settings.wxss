 /* system-settings.wxss */
.container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.setting-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-left: 16rpx;
}

.card-content {
  padding: 32rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.setting-value {
  display: flex;
  align-items: center;
}

.time-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #0052d9;
  margin-right: 8rpx;
}

.time-unit {
  font-size: 24rpx;
  color: #999999;
}

.setting-control {
  display: flex;
  align-items: center;
}

.input-section {
  margin: 24rpx 0;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.input-suffix {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.time-input {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  width: 200rpx;
  display: inline-block;
}

/* 输入框文本居中 */
.time-input .t-input__control {
  text-align: center;
}

.setting-description {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  margin-top: 16rpx;
}



.normal-text {
  color: #00a870;
  font-weight: 500;
}

.action-section {
  margin-top: 48rpx;
  padding: 0 32rpx;
}

.save-btn {
  border-radius: 12rpx;
  font-weight: 600;
}

/* 联系信息输入框样式 */
.setting-input {
  flex: 1;
  margin-left: 24rpx;
}

.contact-input {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
}

.contact-textarea {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
  min-height: 120rpx;
  padding: 16rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 24rpx;
  }
  
  .card-header,
  .card-content {
    padding: 24rpx;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .setting-control {
    align-self: flex-end;
  }
  
  .setting-input {
    margin-left: 0;
    width: 100%;
  }
}